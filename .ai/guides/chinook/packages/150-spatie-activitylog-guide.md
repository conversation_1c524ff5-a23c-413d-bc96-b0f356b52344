# Enhanced Spatie ActivityLog Guide

## Table of Contents

- [Overview](#overview)
- [Installation & Configuration](#installation--configuration)
- [Advanced Activity Logging](#advanced-activity-logging)
- [Custom Activity Models](#custom-activity-models)
- [Performance Optimization](#performance-optimization)
- [Security & Compliance](#security--compliance)
- [Real-time Activity Monitoring](#real-time-activity-monitoring)
- [Activity Analytics](#activity-analytics)
- [Integration Patterns](#integration-patterns)
- [Testing Strategies](#testing-strategies)
- [Production Deployment](#production-deployment)
- [Best Practices](#best-practices)

## Overview

Spatie Laravel ActivityLog provides comprehensive audit logging capabilities for Laravel applications. This enhanced guide demonstrates advanced implementation patterns, performance optimization, security compliance, and real-time monitoring for enterprise-grade activity logging.

### Key Features

- **Comprehensive Audit Trails**: Track all model changes and user actions
- **Custom Activity Types**: Define application-specific activity logging
- **Performance Optimized**: Efficient logging with minimal performance impact
- **Security Compliant**: GDPR, SOX, and enterprise compliance features
- **Real-time Monitoring**: Live activity feeds and notifications
- **Advanced Analytics**: Activity reporting and trend analysis

### Architecture Overview

```mermaid
graph TB
    subgraph "Application Layer"
        A[User Actions]
        B[Model Changes]
        C[System Events]
        D[API Requests]
    end
    
    subgraph "Activity Logging Layer"
        E[Activity Logger]
        F[Custom Loggers]
        G[Batch Processor]
        H[Event Listeners]
    end
    
    subgraph "Storage Layer"
        I[Activity Log Table]
        J[Archived Logs]
        K[Search Index]
        L[Analytics Store]
    end
    
    subgraph "Monitoring Layer"
        M[Real-time Feed]
        N[Alert System]
        O[Dashboard]
        P[Reports]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> I
    G --> J
    H --> K
    
    I --> M
    J --> N
    K --> O
    L --> P
    
    style A fill:#1976d2,color:#fff
    style E fill:#388e3c,color:#fff
    style I fill:#f57c00,color:#fff
    style M fill:#d32f2f,color:#fff
```

## Installation & Configuration

### Enhanced Package Installation

```bash
# Install Spatie ActivityLog
composer require spatie/laravel-activitylog

# Install additional dependencies for enhanced features
composer require spatie/laravel-permission
composer require laravel/scout
composer require pusher/pusher-php-server

# Publish and customize migrations
php artisan vendor:publish --provider="Spatie\Activitylog\ActivitylogServiceProvider" --tag="activitylog-migrations"

# Publish configuration
php artisan vendor:publish --provider="Spatie\Activitylog\ActivitylogServiceProvider" --tag="activitylog-config"

# Run migrations
php artisan migrate
```

### Enhanced Configuration

```php
<?php

// config/activitylog.php
return [
    /*
     * If set to false, no activities will be saved to the database.
     */
    'enabled' => env('ACTIVITY_LOGGER_ENABLED', true),

    /*
     * When the clean-command is executed, all recording activities older than
     * the number of days specified here will be deleted.
     */
    'delete_records_older_than_days' => 365,

    /*
     * If no log name is passed to the activity() helper a default log name
     * will be used instead.
     */
    'default_log_name' => 'default',

    /*
     * The default authentication driver used to retrieve the current user.
     */
    'default_auth_driver' => null,

    /*
     * If set to true, the subject returns soft deleted models.
     */
    'subject_returns_soft_deleted_models' => false,

    /*
     * This model will be used to log activity.
     */
    'activity_model' => \App\Models\Activity::class,

    /*
     * This is the name of the table that will be created by the migration and
     * used by the Activity model shipped with this package.
     */
    'table_name' => 'activity_log',

    /*
     * Enhanced configuration for enterprise features
     */
    'enhanced' => [
        /*
         * Enable real-time activity broadcasting
         */
        'real_time_enabled' => env('ACTIVITY_REAL_TIME_ENABLED', true),
        
        /*
         * Broadcasting channel prefix
         */
        'broadcast_channel_prefix' => 'activity',
        
        /*
         * Enable activity search indexing
         */
        'search_enabled' => env('ACTIVITY_SEARCH_ENABLED', true),
        
        /*
         * Batch processing settings
         */
        'batch_processing' => [
            'enabled' => env('ACTIVITY_BATCH_ENABLED', false),
            'batch_size' => 100,
            'queue' => 'activity-logs',
        ],
        
        /*
         * Performance settings
         */
        'performance' => [
            'async_logging' => env('ACTIVITY_ASYNC_LOGGING', true),
            'compress_properties' => true,
            'cache_user_data' => true,
        ],
        
        /*
         * Security and compliance
         */
        'security' => [
            'encrypt_sensitive_data' => env('ACTIVITY_ENCRYPT_SENSITIVE', true),
            'anonymize_ip_addresses' => env('ACTIVITY_ANONYMIZE_IP', false),
            'gdpr_compliance' => env('ACTIVITY_GDPR_COMPLIANCE', true),
        ],
        
        /*
         * Retention policies
         */
        'retention' => [
            'default_days' => 365,
            'critical_activities_days' => 2555, // 7 years for compliance
            'user_activities_days' => 90,
            'system_activities_days' => 30,
        ],
        
        /*
         * Alert thresholds
         */
        'alerts' => [
            'suspicious_activity_threshold' => 10,
            'failed_login_threshold' => 5,
            'bulk_operation_threshold' => 100,
        ],
    ],
];
```

## Advanced Activity Logging

### Enhanced Activity Model

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Models\Activity as BaseActivity;
use Laravel\Scout\Searchable;

class Activity extends BaseActivity
{
    use Searchable;

    protected $fillable = [
        'log_name',
        'description',
        'subject_type',
        'subject_id',
        'causer_type',
        'causer_id',
        'properties',
        'batch_uuid',
        'event',
        'ip_address',
        'user_agent',
        'session_id',
        'risk_level',
        'compliance_category',
    ];

    protected function casts(): array
    {
        return [
            'properties' => 'encrypted:array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'risk_level' => 'integer',
        ];
    }

    /**
     * Get the indexable data array for the model
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'log_name' => $this->log_name,
            'description' => $this->description,
            'event' => $this->event,
            'causer_type' => $this->causer_type,
            'causer_id' => $this->causer_id,
            'subject_type' => $this->subject_type,
            'subject_id' => $this->subject_id,
            'created_at' => $this->created_at->timestamp,
            'risk_level' => $this->risk_level,
            'compliance_category' => $this->compliance_category,
        ];
    }

    /**
     * Scope for high-risk activities
     */
    public function scopeHighRisk(Builder $query): Builder
    {
        return $query->where('risk_level', '>=', 7);
    }

    /**
     * Scope for compliance-related activities
     */
    public function scopeCompliance(Builder $query, string $category = null): Builder
    {
        $query = $query->whereNotNull('compliance_category');
        
        if ($category) {
            $query->where('compliance_category', $category);
        }
        
        return $query;
    }

    /**
     * Scope for user activities
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('causer_id', $userId)
            ->where('causer_type', 'App\Models\User');
    }

    /**
     * Scope for recent activities
     */
    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get formatted risk level
     */
    public function getRiskLevelTextAttribute(): string
    {
        return match ($this->risk_level) {
            1, 2, 3 => 'Low',
            4, 5, 6 => 'Medium',
            7, 8, 9 => 'High',
            10 => 'Critical',
            default => 'Unknown'
        };
    }

    /**
     * Get activity context
     */
    public function getContextAttribute(): array
    {
        return [
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
            'session_id' => $this->session_id,
            'timestamp' => $this->created_at,
            'risk_level' => $this->risk_level_text,
        ];
    }

    /**
     * Check if activity is suspicious
     */
    public function isSuspicious(): bool
    {
        return $this->risk_level >= 7 || 
               $this->log_name === 'security' ||
               str_contains($this->description, 'failed') ||
               str_contains($this->description, 'unauthorized');
    }
}
```

### Enhanced Activity Traits

```php
<?php

namespace App\Traits;

use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Facades\Auth;

trait EnhancedLogsActivity
{
    use LogsActivity;

    /**
     * Get enhanced activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->getLoggableAttributes())
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->useLogName($this->getActivityLogName())
            ->setDescriptionForEvent(fn(string $eventName) => $this->getActivityDescription($eventName));
    }

    /**
     * Get loggable attributes for this model
     */
    protected function getLoggableAttributes(): array
    {
        // Override in child classes to specify which attributes to log
        return $this->fillable ?? ['*'];
    }

    /**
     * Get activity log name for this model
     */
    protected function getActivityLogName(): string
    {
        return strtolower(class_basename($this));
    }

    /**
     * Get activity description for event
     */
    protected function getActivityDescription(string $eventName): string
    {
        $modelName = class_basename($this);
        $userName = Auth::user()?->name ?? 'System';
        
        return match ($eventName) {
            'created' => "{$userName} created {$modelName} #{$this->id}",
            'updated' => "{$userName} updated {$modelName} #{$this->id}",
            'deleted' => "{$userName} deleted {$modelName} #{$this->id}",
            'restored' => "{$userName} restored {$modelName} #{$this->id}",
            default => "{$userName} performed {$eventName} on {$modelName} #{$this->id}"
        };
    }

    /**
     * Add custom properties to activity log
     */
    public function tapActivity($activity, string $eventName): void
    {
        $activity->ip_address = request()->ip();
        $activity->user_agent = request()->userAgent();
        $activity->session_id = session()->getId();
        $activity->risk_level = $this->calculateRiskLevel($eventName);
        $activity->compliance_category = $this->getComplianceCategory($eventName);
        
        // Add custom properties
        $customProperties = $this->getCustomActivityProperties($eventName);
        if (!empty($customProperties)) {
            $activity->properties = array_merge(
                $activity->properties->toArray(),
                $customProperties
            );
        }
    }

    /**
     * Calculate risk level for activity
     */
    protected function calculateRiskLevel(string $eventName): int
    {
        return match ($eventName) {
            'deleted' => 8,
            'updated' => 5,
            'created' => 3,
            'restored' => 6,
            default => 1
        };
    }

    /**
     * Get compliance category for activity
     */
    protected function getComplianceCategory(string $eventName): ?string
    {
        // Override in models that require compliance tracking
        return null;
    }

    /**
     * Get custom properties for activity
     */
    protected function getCustomActivityProperties(string $eventName): array
    {
        // Override in child classes to add custom properties
        return [];
    }
}
```

## Custom Activity Models

### User Activity Tracking

```php
<?php

namespace App\Models;

use App\Traits\EnhancedLogsActivity;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use EnhancedLogsActivity, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'email_verified_at',
        'last_login_at', 'login_count', 'is_active'
    ];

    protected function getLoggableAttributes(): array
    {
        return ['name', 'email', 'is_active', 'email_verified_at'];
    }

    protected function getComplianceCategory(string $eventName): ?string
    {
        return match ($eventName) {
            'created' => 'user_management',
            'updated' => 'user_data_change',
            'deleted' => 'user_deletion',
            default => 'user_activity'
        };
    }

    protected function calculateRiskLevel(string $eventName): int
    {
        return match ($eventName) {
            'deleted' => 9, // High risk for user deletion
            'updated' => $this->wasEmailChanged() ? 8 : 5,
            'created' => 4,
            default => 2
        };
    }

    protected function getCustomActivityProperties(string $eventName): array
    {
        $properties = [
            'user_roles' => $this->roles->pluck('name')->toArray(),
            'login_count' => $this->login_count,
        ];

        if ($eventName === 'updated' && $this->wasEmailChanged()) {
            $properties['email_change'] = [
                'old_email' => $this->getOriginal('email'),
                'new_email' => $this->email,
                'requires_verification' => true,
            ];
        }

        return $properties;
    }

    private function wasEmailChanged(): bool
    {
        return $this->isDirty('email');
    }
}
```

### Security Activity Logger

```php
<?php

namespace App\Services;

use App\Models\Activity;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class SecurityActivityLogger
{
    /**
     * Log authentication attempt
     */
    public function logAuthenticationAttempt(string $email, bool $successful, Request $request): void
    {
        $description = $successful
            ? "Successful login for {$email}"
            : "Failed login attempt for {$email}";

        activity('security')
            ->withProperties([
                'email' => $email,
                'successful' => $successful,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'attempt_time' => now(),
                'session_id' => $request->session()->getId(),
                'risk_level' => $successful ? 2 : 6,
            ])
            ->log($description);

        // Check for suspicious activity
        if (!$successful) {
            $this->checkForSuspiciousActivity($email, $request->ip());
        }
    }

    /**
     * Log permission changes
     */
    public function logPermissionChange(int $userId, array $oldRoles, array $newRoles): void
    {
        $addedRoles = array_diff($newRoles, $oldRoles);
        $removedRoles = array_diff($oldRoles, $newRoles);

        activity('security')
            ->causedBy(Auth::id())
            ->performedOn(User::find($userId))
            ->withProperties([
                'old_roles' => $oldRoles,
                'new_roles' => $newRoles,
                'added_roles' => $addedRoles,
                'removed_roles' => $removedRoles,
                'risk_level' => 8,
                'compliance_category' => 'access_control',
            ])
            ->log('User roles modified');
    }

    /**
     * Check for suspicious activity patterns
     */
    private function checkForSuspiciousActivity(string $email, string $ipAddress): void
    {
        $recentFailures = Activity::where('log_name', 'security')
            ->where('description', 'like', "Failed login attempt for {$email}")
            ->where('created_at', '>=', now()->subMinutes(15))
            ->count();

        if ($recentFailures >= 5) {
            activity('security')
                ->withProperties([
                    'alert_type' => 'suspicious_login_attempts',
                    'email' => $email,
                    'ip_address' => $ipAddress,
                    'failure_count' => $recentFailures,
                    'risk_level' => 10,
                ])
                ->log("ALERT: {$recentFailures} failed login attempts for {$email}");
        }
    }
}
```

## Performance Optimization

### Activity Cache Service

```php
<?php

namespace App\Services;

use App\Models\Activity;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class ActivityCacheService
{
    /**
     * Cache recent activities for a user
     */
    public function cacheUserActivities(int $userId, int $limit = 50): Collection
    {
        $cacheKey = "user_activities_{$userId}_{$limit}";

        return Cache::remember($cacheKey, 300, function () use ($userId, $limit) {
            return Activity::byUser($userId)
                ->with(['subject', 'causer'])
                ->latest()
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache activity statistics
     */
    public function cacheActivityStats(): array
    {
        return Cache::remember('activity_stats', 600, function () {
            return [
                'total_activities' => Activity::count(),
                'activities_today' => Activity::whereDate('created_at', today())->count(),
                'high_risk_activities' => Activity::highRisk()->count(),
                'compliance_activities' => Activity::compliance()->count(),
            ];
        });
    }
}
```

## Testing Strategies

### Activity Logging Tests

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Track;
use App\Models\Activity;
use App\Services\SecurityActivityLogger;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ActivityLoggingTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_creation_is_logged(): void
    {
        $user = User::factory()->create(['name' => 'Test User']);

        $this->assertDatabaseHas('activity_log', [
            'subject_type' => User::class,
            'subject_id' => $user->id,
            'log_name' => 'user',
        ]);
    }

    public function test_model_update_logs_only_changed_attributes(): void
    {
        $track = Track::factory()->create(['name' => 'Original Name']);

        $track->update(['name' => 'Updated Name']);

        $activity = Activity::latest()->first();

        $this->assertEquals('updated', $activity->event);
        $this->assertArrayHasKey('name', $activity->properties['attributes']);
    }

    public function test_security_activity_logging(): void
    {
        $logger = app(SecurityActivityLogger::class);
        $request = request();

        $logger->logAuthenticationAttempt('<EMAIL>', false, $request);

        $this->assertDatabaseHas('activity_log', [
            'log_name' => 'security',
            'description' => 'Failed login <NAME_EMAIL>',
        ]);
    }

    public function test_high_risk_activities_are_flagged(): void
    {
        $user = User::factory()->create();

        activity('security')
            ->causedBy($user)
            ->withProperties(['risk_level' => 9])
            ->log('Suspicious activity detected');

        $activity = Activity::latest()->first();

        $this->assertTrue($activity->isSuspicious());
    }
}
```

## Best Practices

### Implementation Guidelines

1. **Performance Considerations**
   - Use async logging for high-volume applications
   - Implement proper caching strategies
   - Consider database partitioning for large datasets
   - Use batch processing for bulk operations

2. **Security & Compliance**
   - Encrypt sensitive data in activity properties
   - Implement proper retention policies
   - Ensure GDPR compliance with data anonymization
   - Regular security audits of logged data

3. **Monitoring & Alerting**
   - Set up real-time alerts for suspicious activities
   - Monitor activity log performance metrics
   - Implement automated cleanup processes
   - Regular backup of critical activity data

This comprehensive Enhanced Spatie ActivityLog guide provides enterprise-grade activity logging with advanced features including real-time monitoring, security compliance, performance optimization, and comprehensive testing strategies.

---

**Next Steps:**

- Explore [Modern Testing with Pest Guide](../testing/010-pest-testing-guide.md) for comprehensive testing strategies
- Review [Development Debugging Tools Guide](../development/010-debugbar-guide.md) for debugging and profiling
- Check [Laravel WorkOS Guide](090-laravel-workos-guide.md) for enterprise authentication integration
